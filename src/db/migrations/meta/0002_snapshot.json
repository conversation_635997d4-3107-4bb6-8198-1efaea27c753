{"id": "208608de-197f-4d00-abbc-50f16a98b1f0", "prevId": "e9b9d002-3ba4-486e-a3bc-03c27984771c", "version": "7", "dialect": "postgresql", "tables": {"public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "durationInMins": {"name": "durationInMins", "type": "integer", "primaryKey": false, "notNull": false}, "clerkUserId": {"name": "clerkUserId", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created": {"name": "created", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated": {"name": "updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"clerkUserIndex": {"name": "clerkUserIndex", "columns": [{"expression": "clerkUserId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schedule_availability": {"name": "schedule_availability", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "scheduleId": {"name": "scheduleId", "type": "uuid", "primaryKey": false, "notNull": false}, "startTime": {"name": "startTime", "type": "text", "primaryKey": false, "notNull": false}, "endTime": {"name": "endTime", "type": "text", "primaryKey": false, "notNull": false}, "dayOfWeek": {"name": "dayOfWeek", "type": "day", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"scheduleIndex": {"name": "scheduleIndex", "columns": [{"expression": "scheduleId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"schedule_availability_scheduleId_schedules_id_fk": {"name": "schedule_availability_scheduleId_schedules_id_fk", "tableFrom": "schedule_availability", "tableTo": "schedules", "columnsFrom": ["scheduleId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schedules": {"name": "schedules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false}, "clerkUserId": {"name": "clerkUserId", "type": "text", "primaryKey": false, "notNull": false}, "created": {"name": "created", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated": {"name": "updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"schedules_clerkUserId_unique": {"name": "schedules_clerkUserId_unique", "nullsNotDistinct": false, "columns": ["clerkUserId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.day": {"name": "day", "schema": "public", "values": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}