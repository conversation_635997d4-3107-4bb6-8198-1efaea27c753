import { DAYS_OF_WEEK } from "@/constants/schemaHelpers";
import { relations } from "drizzle-orm";
import { pgTable, uuid, text, integer, boolean, timestamp, index, pgEnum } from "drizzle-orm/pg-core";

const created = timestamp('created')
  .defaultNow();
const updated = timestamp('updated')
  .defaultNow()
  .$onUpdate(() => new Date());

export const EventTable = pgTable('events', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title'),
  description: text('description'),
  durationInMins: integer('durationInMins'),
  clerkUserId: text('clerkUserId'),
  isActive: boolean('isActive').default(true),
  created,
  updated,
},
  table => ({
    clerkUserIndex: index('clerkUserIndex').on(table?.clerkUserId),
  })
);

export const ScheduleTable = pgTable('schedules', {
  id: uuid('id').primaryKey().defaultRandom(),
  timezone: text('timezone'),
  clerkUserId: text('clerkUserId').unique(),
  created,
  updated,
});

export const scheduleRelations = relations(ScheduleTable, ({ many }) => ({
  availabilities: many(ScheduleAvailabilityTable)
}));

export const scheduleDayOfWeekEnum = pgEnum('day', DAYS_OF_WEEK);

export const ScheduleAvailabilityTable = pgTable('schedule_availability', {
  id: uuid('id').primaryKey().defaultRandom(),
  scheduleId: uuid('scheduleId').references(() => ScheduleTable?.id, { onDelete: "cascade" }),
  startTime: text('startTime'),
  endTime: text('endTime'),
  dayOfWeek: scheduleDayOfWeekEnum('dayOfWeek'),
}, table => ({
  scheduleIndex: index('scheduleIndex').on(table?.scheduleId)
}))

export const scheduleAvailibilityRelations = relations(ScheduleAvailabilityTable, ({ one }) => ({
  availabilities: one(ScheduleTable, {
    fields: [ScheduleAvailabilityTable.scheduleId],
    references: [ScheduleTable.id]
  })
}));
