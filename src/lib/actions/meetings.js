"use server"
import { db } from "@/db/db"
import { getValidTimesFromSchedule } from "../utils/getValidTimesFromSchedule"
import { meetingActionSchema } from "@/schema/meetings"
import { redirect } from "next/navigation"
import { fromZonedTime } from "date-fns-tz"
import { createCalendarEvent } from "@/server/googleCalendar"

export async function createMeeting(unsafeData) {
  const { success, data } = meetingActionSchema.safeParse(unsafeData)

  if (!success) return { error: true }

  const event = await db.query.EventTable.findFirst({
    where: ({ clerkUserId, isActive, id }, { eq, and }) =>
      and(
        eq(isActive, true),
        eq(clerkUserId, data.clerkUserId),
        eq(id, data.eventId)
      ),
  })

  if (event == null) return { error: true }
  const startInTimezone = fromZonedTime(data.startTime, data.timezone)

  const validTimes = await getValidTimesFromSchedule([startInTimezone], event)
  if (validTimes.length === 0) return { error: true }

  await createCalendarEvent({
    ...data,
    startTime: startInTimezone,
    durationInMinutes: event.durationInMins,
    eventName: event.title,
  })

  redirect(
    `/book/${data.clerkUserId}/${data.eventId
    }/success?startTime=${data.startTime.toISOString()}`
  )
}
