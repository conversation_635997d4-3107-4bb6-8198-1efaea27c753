"use server"

import { db } from "@/db/db";
import { ScheduleTable, ScheduleAvailabilityTable } from "@/db/schema";
import { scheduleFormSchema } from "@/schema/schedule"
import { currentUser } from "@clerk/nextjs/server"
import { eq } from "drizzle-orm"

export async function saveSchedule(unsafeData) {
  const { id: userId } = await currentUser();
  const { success, data } = scheduleFormSchema.safeParse(unsafeData)

  if (!success || !userId) {
    return { error: true }
  }

  const { availabilities, ...scheduleData } = data

  const [{ id: scheduleId }] = await db
    .insert(ScheduleTable)
    .values({ ...scheduleData, clerkUserId: userId })
    .onConflictDoUpdate({
      target: ScheduleTable.clerkUserId,
      set: scheduleData,
    })
    .returning({ id: ScheduleTable.id })

  const statements = [
    db
      .delete(ScheduleAvailabilityTable)
      .where(eq(ScheduleAvailabilityTable.scheduleId, scheduleId)),
  ]

  if (availabilities.length > 0) {
    statements.push(
      db.insert(ScheduleAvailabilityTable).values(
        availabilities.map(availability => ({
          ...availability,
          scheduleId,
        }))
      )
    )
  }

  await db.batch(statements)
}
