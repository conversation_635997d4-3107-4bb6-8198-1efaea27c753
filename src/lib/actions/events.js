"use server"

import { db } from "@/db/db"
import { EventTable } from "@/db/schema"
import { eventFormSchema } from "@/schema/events"
import { currentUser } from "@clerk/nextjs/server"
import { and, eq } from "drizzle-orm"
import { redirect } from "next/navigation"

export async function createEvent(unsafeData) {
  const { id: userId } = await currentUser();
  const { success, data } = eventFormSchema.safeParse(unsafeData)

  if (!success || !userId) {
    return { error: true }
  }

  await db.insert(EventTable).values({ ...data, clerkUserId: userId })

  redirect("/events")
}

export async function updateEvent(id, unsafeData) {
  const { id: userId } = await currentUser();
  const { success, data } = eventFormSchema.safeParse(unsafeData);
  if (!success || !userId) {
    return { error: true }
  }
  const { rowCount } = await db
    .update(EventTable)
    .set({ ...data })
    .where(and(eq(EventTable.id, id), eq(EventTable.clerkUserId, userId)));

  if (rowCount === 0) {
    return { error: true }
  }

  redirect("/events")
}

export async function deleteEvent(id) {
  const { id: userId } = await currentUser();

  if (!userId) {
    return { error: true }
  }

  const { rowCount } = await db
    .delete(EventTable)
    .where(and(eq(EventTable.id, id), eq(EventTable.clerkUserId, userId)))

  if (rowCount === 0) {
    return { error: true }
  }

  redirect("/events")
}
