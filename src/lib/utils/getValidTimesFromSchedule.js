import { db } from "@/db/db"
import { getCalendarEventTimes } from "@/server/googleCalendar"
import {
  addMinutes,
  areIntervalsOverlapping,
  isFriday,
  isMonday,
  isSaturday,
  isSunday,
  isThursday,
  isTuesday,
  isWednesday,
  isWithinInterval,
  setHours,
  setMinutes,
} from "date-fns"
import { fromZonedTime } from "date-fns-tz"

export async function getValidTimesFromSchedule(
  timesInOrder,
  event,
) {
  const start = timesInOrder[0]
  const end = timesInOrder.at(-1)

  if (start == null || end == null) return []

  const schedule = await db.query.ScheduleTable.findFirst({
    where: ({ clerkUserId: userIdCol }, { eq }) =>
      eq(userIdCol, event.clerkUserId),
    with: { availabilities: true },
  })

  if (schedule == null) return []

  const groupedAvailabilities = Object.groupBy(
    schedule.availabilities,
    a => a.dayOfWeek
  )

  const eventTimes = await getCalendarEventTimes(event.clerkUserId, {
    start,
    end,
  })

  return timesInOrder.filter(intervalDate => {
    const availabilities = getAvailabilities(
      groupedAvailabilities,
      intervalDate,
      schedule.timezone
    )
    const eventInterval = {
      start: intervalDate,
      end: addMinutes(intervalDate, event.durationInMinutes),
    }

    return (
      eventTimes.every(eventTime => {
        return !areIntervalsOverlapping(eventTime, eventInterval)
      }) &&
      availabilities.some(availability => {
        return (
          isWithinInterval(eventInterval.start, availability) &&
          isWithinInterval(eventInterval.end, availability)
        )
      })
    )
  })
}

function getAvailabilities(groupedAvailabilities, date, timezone) {
  let availabilities;

  if (isMonday(date)) {
    availabilities = groupedAvailabilities.monday
  }
  if (isTuesday(date)) {
    availabilities = groupedAvailabilities.tuesday
  }
  if (isWednesday(date)) {
    availabilities = groupedAvailabilities.wednesday
  }
  if (isThursday(date)) {
    availabilities = groupedAvailabilities.thursday
  }
  if (isFriday(date)) {
    availabilities = groupedAvailabilities.friday
  }
  if (isSaturday(date)) {
    availabilities = groupedAvailabilities.saturday
  }
  if (isSunday(date)) {
    availabilities = groupedAvailabilities.sunday
  }

  if (availabilities == null) return []

  return availabilities.map(({ startTime, endTime }) => {
    const start = fromZonedTime(
      setMinutes(
        setHours(date, parseInt(startTime.split(":")[0])),
        parseInt(startTime.split(":")[1])
      ),
      timezone
    )

    const end = fromZonedTime(
      setMinutes(
        setHours(date, parseInt(endTime.split(":")[0])),
        parseInt(endTime.split(":")[1])
      ),
      timezone
    )

    return { start, end }
  })
}
