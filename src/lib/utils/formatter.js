export function formatEventDescription(durationInMins) {
  const hours = Math.floor(durationInMins / 60)
  const minutes = durationInMins % 60
  const minutesString = `${minutes} ${minutes > 1 ? "mins" : "min"}`
  const hoursString = `${hours} ${hours > 1 ? "hrs" : "hr"}`

  if (hours === 0) return minutesString
  if (minutes === 0) return hoursString
  return `${hoursString} ${minutesString}`
}

export function formatTimezoneOffset(timezone) {
  return new Intl.DateTimeFormat(undefined, {
    timeZone: timezone,
    timeZoneName: "shortOffset",
  })
    .formatToParts(new Date())
    .find(part => part.type == "timeZoneName")?.value
}

const dateFormatter = new Intl.DateTimeFormat(undefined, {
  dateStyle: "medium",
})

export function formatDate(date) {
  return dateFormatter.format(date)
}

const timeFormatter = new Intl.DateTimeFormat(undefined, {
  timeStyle: "short",
})

export function formatTimeString(date) {
  return timeFormatter.format(date)
}

const dateTimeFormatter = new Intl.DateTimeFormat(undefined, {
  dateStyle: "medium",
  timeStyle: "short",
})

export function formatDateTime(date) {
  return dateTimeFormatter.format(date)
}
