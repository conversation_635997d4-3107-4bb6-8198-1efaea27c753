"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"

export function ModeToggle() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button variant="outline" size="icon">
        <Sun size={18} /> {/* or a spinner */}
      </Button>
    )
  }


  return (
    <Button variant="outline" size="icon" onClick={() => theme === 'light' ? setTheme('dark') : setTheme('light')}>
      {
        theme === 'light' ? <Moon className="h-[1.2rem] w-[1.2rem]" /> : <Sun className="h-[1.2rem] w-[1.2rem]" />
      }
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
};
