import { <PERSON><PERSON> } from "@/components/ui/button";
import { SignIn<PERSON>utton, SignUpButton, UserButton } from "@clerk/nextjs";
import { currentUser } from '@clerk/nextjs/server'
import { redirect } from "next/navigation";

export default async function Home() {
  const userInfo = await currentUser();
  if (userInfo?.id) {
    redirect('/events')
  }

  return (
    <div className="text-center container my-4 mx-auto">
      <h1 className="text-3xl font-semibold mb-4">Home</h1>
      <div className="flex items-center gap-4">
        <Button className={'border border-primary-foreground'} asChild>
          <SignInButton />
        </Button>
        <Button className={'border border-primary-foreground'} asChild>
          <SignUpButton />
        </Button>
        <UserButton />
      </div>
    </div>
  );
}
