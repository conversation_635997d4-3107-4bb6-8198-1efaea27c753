import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent, CardDescription, CardTitle } from "@/components/ui/card";
import { db } from "@/db/db";
import { RedirectToSignIn } from "@clerk/nextjs";
import { currentUser } from '@clerk/nextjs/server';
import { ScheduleForm } from "@/components/main/schedule/Form";

export default async function SchedulePage() {
  const userInfo = await currentUser();
  if (!userInfo?.id) {
    return <RedirectToSignIn />;
  }

  const schedule = await db.query.ScheduleTable.findFirst({
    where: ({ clerkUserId }, { eq }) => eq(clerkUserId, userInfo?.id),
    with: { availabilities: true },
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule</CardTitle>
        <CardDescription>Create a new schedule</CardDescription>
      </CardHeader>
      <CardContent>
        <ScheduleForm schedule={schedule} />
      </CardContent>
    </Card>
  )
};
