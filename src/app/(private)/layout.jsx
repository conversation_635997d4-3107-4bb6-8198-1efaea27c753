import { UserButton } from "@clerk/nextjs";
import { CalendarRange } from "lucide-react";
import Navlink from "./components/Navlink";
import { ModeToggle } from "@/components/ui/mode-toggle";

export default function PrivateLayout({ children }) {
  return (
    <>
      <header className="flex py-2 px-8 border-b bg-card">
        <nav className="font-medium flex items-center justify-between text-sm gap-6 container">
          <div className="flex items-center gap-2 font-semibold">
            <CalendarRange className="size-6" />
            <span className="sr-only md:not-sr-only">Appointer</span>
          </div>

          <div className="flex items-center gap-2">
            <Navlink href='/events'>Events</Navlink>
            <Navlink href='/schedule'>Schedule</Navlink>
          </div>

          <div className="flex items-center gap-2">
            <ModeToggle />
            <UserButton appearance={{ elements: { userButtonAvatarBox: 'size-full' } }} />
          </div>
        </nav>
      </header>
      <main className="container my-6 mx-auto">{children}</main>
    </>
  )
}
