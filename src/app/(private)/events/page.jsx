import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardDescription, <PERSON>Footer, CardTitle } from "@/components/ui/card";
import { CopyEventButton } from "@/components/utils/CopyEventButton";
import { db } from "@/db/db";
import { cn } from "@/lib/utils";
import { formatEventDescription } from "@/lib/utils/formatter";
import { RedirectToSignIn } from "@clerk/nextjs";
import { currentUser } from '@clerk/nextjs/server';
import { CalendarPlus, CalendarRange } from "lucide-react";
import Link from "next/link";

export default async function EventsPage() {
  const userInfo = await currentUser();
  if (!userInfo?.id) {
    return <RedirectToSignIn />;
  }
  const events = await db.query.EventTable.findMany({
    where: ({ clerkUserId }, { eq }) => eq(clerkUserId, userInfo?.id),
    orderBy: ({ created }, { desc }) => desc(created),
  });

  console.log(events);

  return (
    <>
      <div className="flex flex-col md:flex-row items-center justify-between gap-2">
        <h1 className="text-3xl font-semibold mb-6"> Events </h1>
        <Button asChild>
          <Link href={'/events/new'}>
            <CalendarPlus className="size-6" /> New Event
          </Link>
        </Button>
      </div>
      {
        events?.length > 0 ? (
          <div className="grid gap-4 grid-cols-[repeat(auto-fill,minmax(400px,1fr)]">
            {events?.map((event) => (
              <EventCard key={event?.id} {...event} />
            ))}
          </div>
        ) : (
          <Card className={'text-center grid justify-center items-center bg-secondary'}>
            <CardContent className={'space-y-4'}>
              <CardTitle className={'flex items-center justify-center'}><CalendarRange className="size-24 text-muted-foreground" /></CardTitle>
              <CardDescription>Hmm... Feels Empty here, Create your first event to get started!</CardDescription>
              <Button size={'lg'} className={'text-lg'} asChild>
                <Link href={'/events/new'}>
                  <CalendarPlus className="size-6" />
                  Create New Event
                </Link>
              </Button>
            </CardContent>
          </Card>
        )
      }
    </>
  )
};

function EventCard({
  id,
  isActive,
  title,
  description,
  durationInMins,
  clerkUserId,
}) {
  return (
    <Card className={cn("flex flex-col", !isActive && "border-secondary/50")}>
      <CardHeader className={cn(!isActive && "opacity-50")}>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          {formatEventDescription(durationInMins)}
        </CardDescription>
      </CardHeader>
      {description != null && (
        <CardContent className={cn(!isActive && "opacity-50")}>
          <CardDescription>
            {description}
          </CardDescription>
        </CardContent>
      )}
      <CardFooter className="flex justify-end gap-2 mt-auto">
        {isActive && (
          <CopyEventButton
            variant="outline"
            eventId={id}
            clerkUserId={clerkUserId}
          />
        )}
        <Button asChild>
          <Link href={`/events/${id}/edit`}>Edit</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
