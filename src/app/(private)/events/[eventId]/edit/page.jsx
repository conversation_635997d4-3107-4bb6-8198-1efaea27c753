import { EventForm } from "@/components/main/events/Form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, } from "@/components/ui/card";
import { db } from "@/db/db";
import { currentUser } from "@clerk/nextjs/server";
import { notFound } from "next/navigation";

export default async function EditEventPage({ params }) {
  const { eventId } = await params
  const userInfo = await currentUser();
  if (!userInfo?.id) {
    return notFound();
  }

  const event = await db.query.EventTable.findFirst({
    where: (({ id, clerkUserId }, { and, eq }) => and(eq(clerkUserId, userInfo?.id), eq(id, eventId)))
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Edit Event</CardTitle>
        <CardDescription>Edit a existing event</CardDescription>
      </CardHeader>
      <CardContent>
        <EventForm event={event} />
      </CardContent>
    </Card>
  )
}

